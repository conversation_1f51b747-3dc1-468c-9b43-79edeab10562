CE转易语言转换器 - 打包为EXE说明
=====================================

方法一：使用批处理文件（推荐）
1. 双击运行 build_simple.bat
2. 等待安装PyInstaller和打包完成
3. 在dist文件夹中找到生成的exe文件

方法二：手动命令行打包
1. 打开命令提示符（cmd）
2. 切换到当前目录
3. 运行以下命令：

   pip install pyinstaller
   pyinstaller --onefile --windowed --name "CE转易语言转换器" ce_to_epl_gui.py

方法三：使用spec文件
1. 运行命令：pyinstaller ce_converter.spec

打包参数说明：
- --onefile: 打包成单个exe文件
- --windowed: 不显示控制台窗口
- --name: 指定exe文件名称

注意事项：
1. 确保已安装Python 3.6+
2. 首次打包需要下载依赖，可能需要几分钟
3. 生成的exe文件在dist文件夹中
4. exe文件较大（约10-20MB）是正常的，因为包含了Python运行时

如果遇到问题：
1. 检查Python是否正确安装
2. 检查网络连接（下载PyInstaller需要网络）
3. 尝试使用管理员权限运行
4. 如果杀毒软件报警，请添加信任

生成的exe文件可以在没有Python环境的电脑上直接运行！
