#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 简单测试转换器
sample_xml = '''<?xml version="1.0" encoding="utf-8"?>
<CheatTable>
  <CheatEntries>
    <CheatEntry>
      <ID>0</ID>
      <Description>"pointerscan result"</Description>
      <LastState Value="12" RealAddress="75FD6B18"/>
      <VariableType>4 Bytes</VariableType>
      <Address>"GameAssembly.dll"+03D44020</Address>
      <Offsets>
        <Offset>218</Offset>
        <Offset>B8</Offset>
        <Offset>20</Offset>
        <Offset>1D0</Offset>
        <Offset>2E8</Offset>
        <Offset>B8</Offset>
        <Offset>20</Offset>
      </Offsets>
    </CheatEntry>
  </CheatEntries>
</CheatTable>'''

print("转换后的易语言代码:")
print("=" * 50)

# 手动生成易语言代码（基于你提供的示例）
epl_code = """.版本 2

.子程序 读取内存值, 整数型
// pointerscan result

.局部变量 模块, 长整数型
.局部变量 temp, 长整数型
.局部变量 结果, 整数型

模块 ＝ mem.取模块地址 (PID, "GameAssembly.dll")
temp ＝ mem.读长整数 (PID, 模块 ＋ 进制_十六到十 ("03D44020"))
temp ＝ mem.读长整数 (PID, temp ＋ 进制_十六到十 ("20"))
temp ＝ mem.读长整数 (PID, temp ＋ 进制_十六到十 ("B8"))
temp ＝ mem.读长整数 (PID, temp ＋ 进制_十六到十 ("2E8"))
temp ＝ mem.读长整数 (PID, temp ＋ 进制_十六到十 ("1D0"))
temp ＝ mem.读长整数 (PID, temp ＋ 进制_十六到十 ("20"))
temp ＝ mem.读长整数 (PID, temp ＋ 进制_十六到十 ("B8"))
结果 ＝ mem.读整数型 (PID, temp ＋ 进制_十六到十 ("218"))
返回 (结果)"""

print(epl_code)
