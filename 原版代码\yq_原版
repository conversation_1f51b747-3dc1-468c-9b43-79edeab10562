.版本 2

.子程序 键盘_键盘按键, , , 执行模拟按键(无返回值)
.参数 键代码, 整数型, , 键代码
.参数 状态, 整数型, 可空 , 可空:按键(按下+放开)  1 #按键_  3 #按下_  4 #放开_  如果状态大于等于5则为按下与放开之间的延时,可解决某些屏蔽
.参数 是否功能键, 逻辑型, 可空 , 


.版本 2

.子程序 内存_写整数型, 逻辑型, , 
.参数 Process_ID, 整数型, , 
.参数 内存地址, 长整数型, , 
.参数 写入数据, 整数型, , 
.参数 读写方式, 整数型, 可空 , 0 = 默认附加物理读写（若无法读写,可配合[内存_修改属性（）使用]）1 = 强读强写 2 = 无附加CR3物理读写

.版本 2

.子程序 鼠标_右键按下, , , 
.参数 延时, 整数型, 可空 , 

.版本 2

.子程序 内存_取模块地址, 长整数型, , 
.参数 Process_ID, 整数型, , 
.参数 模块名, 文本型, , 
