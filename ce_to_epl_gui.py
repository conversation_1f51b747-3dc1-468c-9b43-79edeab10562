#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CE (Cheat Engine) XML to 易语言 (Easy Language) Converter - GUI版本
带图形界面的CE转换器
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, filedialog, messagebox
import xml.etree.ElementTree as ET
import os


class CEToEPLConverterGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("CE转易语言代码转换器")
        self.root.geometry("1000x700")
        
        # 变量类型映射
        self.variable_types = {
            "4 Bytes": "整数型",
            "8 Bytes": "长整数型", 
            "Float": "小数型",
            "Double": "双精度小数型",
            "2 Bytes": "短整数型",
            "Byte": "字节型",
            "String": "文本型"
        }
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="CE转易语言代码转换器", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # 输入区域
        input_frame = ttk.LabelFrame(main_frame, text="CE XML输入", padding="5")
        input_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        input_frame.columnconfigure(0, weight=1)
        input_frame.rowconfigure(1, weight=1)
        
        # 文件操作按钮
        file_frame = ttk.Frame(input_frame)
        file_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        ttk.Button(file_frame, text="打开文件", command=self.open_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(file_frame, text="清空", command=self.clear_input).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(file_frame, text="示例", command=self.load_example).pack(side=tk.LEFT)
        
        # XML输入文本框
        self.input_text = scrolledtext.ScrolledText(input_frame, height=12, wrap=tk.WORD)
        self.input_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 转换按钮
        convert_frame = ttk.Frame(main_frame)
        convert_frame.grid(row=2, column=0, columnspan=3, pady=10)
        
        ttk.Button(convert_frame, text="转换为易语言代码", 
                  command=self.convert_code, style="Accent.TButton").pack()
        
        # 输出区域
        output_frame = ttk.LabelFrame(main_frame, text="易语言代码输出", padding="5")
        output_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        output_frame.columnconfigure(0, weight=1)
        output_frame.rowconfigure(1, weight=1)
        
        # 输出操作按钮
        output_btn_frame = ttk.Frame(output_frame)
        output_btn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        ttk.Button(output_btn_frame, text="复制代码", command=self.copy_output).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(output_btn_frame, text="保存文件", command=self.save_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(output_btn_frame, text="清空输出", command=self.clear_output).pack(side=tk.LEFT)
        
        # 易语言代码输出文本框
        self.output_text = scrolledtext.ScrolledText(output_frame, height=12, wrap=tk.WORD)
        self.output_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def open_file(self):
        """打开CE文件"""
        file_path = filedialog.askopenfilename(
            title="选择CE文件",
            filetypes=[("CE Table文件", "*.CT"), ("XML文件", "*.xml"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.input_text.delete(1.0, tk.END)
                self.input_text.insert(1.0, content)
                self.status_var.set(f"已加载文件: {os.path.basename(file_path)}")
            except Exception as e:
                messagebox.showerror("错误", f"无法读取文件: {str(e)}")
    
    def clear_input(self):
        """清空输入"""
        self.input_text.delete(1.0, tk.END)
        self.status_var.set("已清空输入")
    
    def load_example(self):
        """加载示例"""
        example_xml = '''<?xml version="1.0" encoding="utf-8"?>
<CheatTable>
  <CheatEntries>
    <CheatEntry>
      <ID>0</ID>
      <Description>"pointerscan result"</Description>
      <LastState Value="12" RealAddress="75FD6B18"/>
      <VariableType>4 Bytes</VariableType>
      <Address>"GameAssembly.dll"+03D44020</Address>
      <Offsets>
        <Offset>218</Offset>
        <Offset>B8</Offset>
        <Offset>20</Offset>
        <Offset>1D0</Offset>
        <Offset>2E8</Offset>
        <Offset>B8</Offset>
        <Offset>20</Offset>
      </Offsets>
    </CheatEntry>
  </CheatEntries>
</CheatTable>'''
        
        self.input_text.delete(1.0, tk.END)
        self.input_text.insert(1.0, example_xml)
        self.status_var.set("已加载示例")
    
    def convert_code(self):
        """转换代码"""
        xml_content = self.input_text.get(1.0, tk.END).strip()
        
        if not xml_content:
            messagebox.showwarning("警告", "请输入CE XML代码")
            return
        
        try:
            entries = self.parse_xml(xml_content)
            if not entries:
                messagebox.showerror("错误", "无法解析XML内容或没有找到CheatEntry")
                return
            
            epl_code = self.generate_epl_code(entries)
            
            self.output_text.delete(1.0, tk.END)
            self.output_text.insert(1.0, epl_code)
            
            self.status_var.set(f"转换完成，共处理 {len(entries)} 个条目")
            
        except Exception as e:
            messagebox.showerror("错误", f"转换失败: {str(e)}")
    
    def parse_xml(self, xml_content):
        """解析XML内容"""
        try:
            root = ET.fromstring(xml_content)
            entries = []
            
            for entry in root.findall('.//CheatEntry'):
                entry_data = {}
                
                # 获取基本信息
                id_elem = entry.find('ID')
                entry_data['id'] = id_elem.text if id_elem is not None else "0"
                
                desc_elem = entry.find('Description')
                entry_data['description'] = desc_elem.text.strip('"') if desc_elem is not None else ""
                
                var_type_elem = entry.find('VariableType')
                entry_data['variable_type'] = var_type_elem.text if var_type_elem is not None else "4 Bytes"
                
                # 解析地址
                address_elem = entry.find('Address')
                if address_elem is not None:
                    address = address_elem.text.strip('"')
                    if '+' in address:
                        parts = address.split('+')
                        entry_data['module'] = parts[0].strip('"')
                        entry_data['base_offset'] = parts[1] if len(parts) > 1 else "0"
                    else:
                        entry_data['module'] = ""
                        entry_data['base_offset'] = address
                
                # 解析偏移链
                offsets_elem = entry.find('Offsets')
                offsets = []
                if offsets_elem is not None:
                    for offset in offsets_elem.findall('Offset'):
                        offsets.append(offset.text)
                
                entry_data['offsets'] = offsets
                entries.append(entry_data)
            
            return entries
            
        except ET.ParseError as e:
            raise Exception(f"XML解析错误: {e}")
    
    def generate_epl_code(self, entries):
        """生成易语言代码"""
        code_lines = [".版本 2", ""]
        
        for entry in entries:
            # 添加注释
            if entry.get('description'):
                code_lines.append(f"// {entry['description']}")
            
            # 函数名
            function_name = f"读取内存值_{entry['id']}" if entry['id'] != "0" else "读取内存值"
            result_type = self.get_epl_type(entry['variable_type'])
            
            code_lines.append(f".子程序 {function_name}, {result_type}")
            code_lines.append("")
            
            # 变量声明
            code_lines.append(".局部变量 模块, 长整数型")
            code_lines.append(".局部变量 temp, 长整数型")
            code_lines.append(f".局部变量 结果, {result_type}")
            code_lines.append("")
            
            # 获取模块地址
            if entry.get('module'):
                code_lines.append(f'模块 ＝ mem.取模块地址 (PID, "{entry["module"]}")')
            else:
                code_lines.append("模块 ＝ 0  // 没有指定模块")
            
            # 基址
            if entry.get('base_offset') and entry['base_offset'] != "0":
                base_offset = self.format_hex(entry['base_offset'])
                code_lines.append(f"temp ＝ mem.读长整数 (PID, 模块 ＋ 进制_十六到十 (\"{base_offset}\"))")
            else:
                code_lines.append("temp ＝ 模块")
            
            # 偏移链
            offsets = entry.get('offsets', [])
            if offsets:
                for offset in offsets[:-1]:
                    hex_offset = self.format_hex(offset)
                    code_lines.append(f"temp ＝ mem.读长整数 (PID, temp ＋ 进制_十六到十 (\"{hex_offset}\"))")
                
                # 最后一个偏移
                final_offset = self.format_hex(offsets[-1])
                read_func = self.get_read_function(entry['variable_type'])
                code_lines.append(f"结果 ＝ mem.{read_func} (PID, temp ＋ 进制_十六到十 (\"{final_offset}\"))")
            else:
                read_func = self.get_read_function(entry['variable_type'])
                code_lines.append(f"结果 ＝ mem.{read_func} (PID, temp)")
            
            code_lines.append("返回 (结果)")
            code_lines.append("")
        
        return "\n".join(code_lines)
    
    def format_hex(self, hex_value):
        """格式化十六进制值"""
        hex_value = hex_value.replace('0x', '').replace('0X', '').upper()
        return hex_value
    
    def get_epl_type(self, ce_type):
        """获取易语言类型"""
        return self.variable_types.get(ce_type, "整数型")
    
    def get_read_function(self, ce_type):
        """获取读取函数"""
        type_to_function = {
            "4 Bytes": "读整数型",
            "8 Bytes": "读长整数",
            "Float": "读小数型", 
            "Double": "读双精度小数型",
            "2 Bytes": "读短整数型",
            "Byte": "读字节型",
            "String": "读文本型"
        }
        return type_to_function.get(ce_type, "读整数型")
    
    def copy_output(self):
        """复制输出到剪贴板"""
        output_content = self.output_text.get(1.0, tk.END).strip()
        if output_content:
            self.root.clipboard_clear()
            self.root.clipboard_append(output_content)
            self.status_var.set("代码已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的内容")
    
    def save_file(self):
        """保存文件"""
        output_content = self.output_text.get(1.0, tk.END).strip()
        if not output_content:
            messagebox.showwarning("警告", "没有可保存的内容")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="保存易语言代码",
            defaultextension=".e",
            filetypes=[("易语言源码", "*.e"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(output_content)
                self.status_var.set(f"已保存到: {os.path.basename(file_path)}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")
    
    def clear_output(self):
        """清空输出"""
        self.output_text.delete(1.0, tk.END)
        self.status_var.set("已清空输出")


def main():
    root = tk.Tk()
    app = CEToEPLConverterGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
