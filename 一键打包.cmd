@echo off
title CE转易语言转换器 - 一键打包
color 0A
echo.
echo  ╔══════════════════════════════════════╗
echo  ║        CE转易语言转换器 - 打包工具        ║
echo  ╚══════════════════════════════════════╝
echo.

echo [1/3] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.6+
    echo    下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

echo.
echo [2/3] 安装PyInstaller...
pip install pyinstaller --quiet --disable-pip-version-check
if %errorlevel% neq 0 (
    echo ❌ PyInstaller安装失败，尝试使用国内镜像...
    pip install pyinstaller -i https://pypi.tuna.tsinghua.edu.cn/simple/ --quiet
)
echo ✅ PyInstaller安装完成

echo.
echo [3/3] 开始打包程序...
echo    这可能需要几分钟时间，请耐心等待...

pyinstaller --onefile --windowed --name "CE转易语言转换器" --distpath "./exe输出" ce_to_epl_gui.py --noconfirm

if %errorlevel% eq 0 (
    echo.
    echo ✅ 打包成功！
    echo.
    echo 📁 EXE文件位置: exe输出\CE转易语言转换器.exe
    echo 📊 正在检查文件信息...
    
    if exist "exe输出\CE转易语言转换器.exe" (
        for %%A in ("exe输出\CE转易语言转换器.exe") do (
            set size=%%~zA
            set /a sizeMB=!size!/1024/1024
        )
        setlocal enabledelayedexpansion
        echo 📏 文件大小: !sizeMB! MB
        echo.
        echo 🎉 打包完成！现在你可以：
        echo    1. 将exe文件分发给其他人使用
        echo    2. 在没有Python的电脑上直接运行
        echo.
        echo 正在打开输出文件夹...
        start "" "exe输出"
    ) else (
        echo ❌ 文件生成失败
    )
) else (
    echo ❌ 打包失败，请检查错误信息
)

echo.
echo 按任意键退出...
pause >nul
