.版本 2
.支持库 spec

.程序集 窗口程序集_启动窗口

.子程序 __启动窗口_创建完毕

mem.安装驱动 ()
PID ＝ mem.进程_取进程ID (“rf4_x64.exe”)

窗口句柄 ＝ 进程_ID取窗口句柄 (PID, , , , )
mem.进程_保护进程 (PID, 3, 真)
' mem.进程_隐藏进程 (-1, 真)

init (6)
获取鱼护数量 ()
大漠.创建 ()
初始化列表框 ()



.子程序 _按钮2_被单击

杆子1地址 ＝ 获取杆子地址 ()
标签1.标题 ＝ 进制_十到十六 (杆子1地址)


.子程序 _时钟1_周期事件

杆子1状态 ＝ mem.内存_读整数型 (PID, 杆子1地址, 0)
杆子2状态 ＝ mem.内存_读整数型 (PID, 杆子2地址, 0)
杆子3状态 ＝ mem.内存_读整数型 (PID, 杆子3地址, 0)
' 调试输出 (杆子1状态, 杆子2状态, 杆子3状态)
.如果真 (正在处理 ＝ 假)
    .如果 (杆子1状态 ＝ 1)
        处理收杆_手竿 (1)
    .否则
        .如果 (杆子2状态 ＝ 1)
            处理收杆_手竿 (2)
        .否则
            .如果 (杆子3状态 ＝ 1)
                处理收杆_手竿 (3)
            .否则

            .如果结束

        .如果结束

    .如果结束

.如果真结束


.子程序 _按钮3_被单击

杆子2地址 ＝ 获取杆子地址 ()
标签2.标题 ＝ 进制_十到十六 (杆子2地址)


.子程序 _按钮4_被单击

杆子3地址 ＝ 获取杆子地址 ()
标签3.标题 ＝ 进制_十到十六 (杆子3地址)


.子程序 处理收杆_手竿
.参数 杆子索引, 整数型
.局部变量 启动时间, 文本型
.局部变量 收杆结束时间, 文本型
.局部变量 间隔, 双精度小数型
.局部变量 n, 整数型
.局部变量 杆子按键, 整数型
.局部变量 当前杆子状态, 整数型
.局部变量 i, 整数型
.局部变量 随机延迟, 整数型
.局部变量 最大循环次数, 整数型
.局部变量 鱼的数量, 整数型
.局部变量 手里的杆子, 整数型

正在处理 ＝ 真
窗口_将焦点切换到指定的窗口 (窗口句柄, 真)


启动时间 ＝ 时间_到时间戳 ()

n ＝ 0



.如果 (杆子索引 ＝ 1)
    杆子按键 ＝ #键1
    当前杆子状态 ＝ 杆子1状态
.否则
    .如果 (杆子索引 ＝ 2)
        杆子按键 ＝ #键2
        当前杆子状态 ＝ 杆子2状态
    .否则
        .如果 (杆子索引 ＝ 3)
            杆子按键 ＝ #键3
            当前杆子状态 ＝ 杆子3状态
        .否则

        .如果结束

    .如果结束

.如果结束
' 上钩一段时间再拿杆子
.判断循环首 (n ＜ 1 且 当前杆子状态 ≠ 0)
    随机延迟 ＝ 取随机数 (8, 10)
    延迟 (随机延迟)
    n ＝ n ＋ 1
.判断循环尾 ()

dd键鼠.KeyPress (杆子按键, 1)


n ＝ 0
最大循环次数 ＝ 取随机数 (900, 1200)

鱼的数量 ＝ 获取鱼护数量 ()
dd键鼠.LeftDown (1)
dd键鼠.KeyDown (#Shift键, 1)

.判断循环首 (当前杆子状态 ≠ 0)
    .如果 (杆子索引 ＝ 1)
        当前杆子状态 ＝ 杆子1状态
    .否则
        .如果 (杆子索引 ＝ 2)
            当前杆子状态 ＝ 杆子2状态
        .否则
            .如果 (杆子索引 ＝ 3)
                当前杆子状态 ＝ 杆子3状态
            .否则

            .如果结束

        .如果结束

    .如果结束

    延迟 (10)
    n ＝ n ＋ 1


    .如果真 (n ＞ 最大循环次数)


        随机延迟 ＝ 取随机数 (25, 60)
        延迟 (随机延迟)
        dd键鼠.LeftUp (1)

        秒收鱼 ()
        跳出循环 ()
    .如果真结束

.判断循环尾 ()







dd键鼠.LeftUp (1)
dd键鼠.KeyUp (#Shift键, 1)

收杆结束时间 ＝ 时间_到时间戳 ()


间隔 ＝ 到整数 (收杆结束时间) － 到整数 (启动时间)

.如果真 (间隔 ＞ 2300)
    n ＝ 0
    最大循环次数 ＝ 取随机数 (450, 550)
    .判断循环首 (判断是否需要按空格 () ＝ 0)
        n ＝ n ＋ 1
        随机延迟 ＝ 取随机数 (8, 20)
        延迟 (随机延迟)
        .如果真 (n ＞ 最大循环次数)
            跳出循环 ()
        .如果真结束

    .判断循环尾 ()

    n ＝ 0
    最大循环次数 ＝ 取随机数 (35, 55)
    .判断循环首 (判断是否需要按空格 () ＝ 1)
        n ＝ n ＋ 1
        随机延迟 ＝ 取随机数 (120, 280)
        延迟 (随机延迟)

        .如果真 (n ＞ 最大循环次数)
            跳出循环 ()
        .如果真结束

        .如果 (鱼的数量 ≥ 95)
            dd键鼠.KeyPress (#退格键, 1)

        .否则
            dd键鼠.KeyPress (#空格键, 1)

        .如果结束

    .判断循环尾 ()

    窗口_将焦点切换到指定的窗口 (窗口句柄, 真)
    随机延迟 ＝ 取随机数 (180, 280)
    延时 (随机延迟)

    dd键鼠.KeyDown (#Shift键, 1)
    dd键鼠.LeftDown (1)

    随机延迟 ＝ 取随机数 (180, 280)
    延时 (随机延迟)
    dd键鼠.LeftUp (1)
    随机延迟 ＝ 取随机数 (40, 80)
    延迟 (随机延迟)
    dd键鼠.KeyUp (#Shift键, 1)
    随机延迟 ＝ 取随机数 (1400, 1700)
    延时 (随机延迟)

.如果真结束



' dd键鼠.KeyPress (#键0, 1)
手里的杆子 ＝ 杆子索引
正在处理 ＝ 假

.子程序 处理收杆_飞德
.参数 杆子索引, 整数型
.局部变量 启动时间, 文本型
.局部变量 收杆结束时间, 文本型
.局部变量 间隔, 双精度小数型
.局部变量 n, 整数型
.局部变量 杆子按键, 整数型
.局部变量 当前杆子状态, 整数型
.局部变量 i, 整数型
.局部变量 随机延迟, 整数型
.局部变量 最大循环次数, 整数型
.局部变量 鱼的数量, 整数型
.局部变量 手里的杆子, 整数型

正在处理 ＝ 真
窗口_将焦点切换到指定的窗口 (窗口句柄, 真)


启动时间 ＝ 时间_到时间戳 ()

n ＝ 0



.如果 (杆子索引 ＝ 1)
    杆子按键 ＝ #键1
    当前杆子状态 ＝ 杆子1状态
.否则
    .如果 (杆子索引 ＝ 2)
        杆子按键 ＝ #键2
        当前杆子状态 ＝ 杆子2状态
    .否则
        .如果 (杆子索引 ＝ 3)
            杆子按键 ＝ #键3
            当前杆子状态 ＝ 杆子3状态
        .否则

        .如果结束

    .如果结束

.如果结束
' 上钩一段时间再拿杆子
.判断循环首 (n ＜ 1 且 当前杆子状态 ≠ 0)
    随机延迟 ＝ 取随机数 (8, 10)
    延迟 (随机延迟)
    n ＝ n ＋ 1
.判断循环尾 ()

dd键鼠.KeyPress (杆子按键, 1)


n ＝ 0
最大循环次数 ＝ 取随机数 (900, 1200)

鱼的数量 ＝ 获取鱼护数量 ()

' dd键鼠.KeyDown (#Shift键, 1)

.判断循环首 (当前杆子状态 ≠ 0)
    .如果 (杆子索引 ＝ 1)
        当前杆子状态 ＝ 杆子1状态
    .否则
        .如果 (杆子索引 ＝ 2)
            当前杆子状态 ＝ 杆子2状态
        .否则
            .如果 (杆子索引 ＝ 3)
                当前杆子状态 ＝ 杆子3状态
            .否则

            .如果结束

        .如果结束

    .如果结束
    dd键鼠.LeftClick (1)

    延迟 (10)
    dd键鼠.RightClick (1)

    n ＝ n ＋ 1


    .' 如果真 (n ＞ 最大循环次数)
        .' 如果真 (选择框5.选中 ＝ 真)
            ' ' 秒收鱼 ()
        .如果真结束
        ' 随机延迟 ＝ 取随机数 (88, 129)
        ' 延迟 (随机延迟)
        ' dd键鼠.LeftUp (1)
        ' dd键鼠.RightUp (1)
        ' 跳出循环 ()
    .如果真结束

.判断循环尾 ()


dd键鼠.LeftUp (1)
延迟 (100)
dd键鼠.RightUp (1)


' dd键鼠.KeyUp (#Shift键, 1)
收杆结束时间 ＝ 时间_到时间戳 ()

间隔 ＝ 到整数 (收杆结束时间) － 到整数 (启动时间)
调试输出 (取现行时间 (), 3)
.如果真 (间隔 ＞ 2300)
    n ＝ 0
    最大循环次数 ＝ 取随机数 (850, 950)
    .判断循环首 (判断是否需要按空格 () ＝ 0)
        n ＝ n ＋ 1
        随机延迟 ＝ 取随机数 (8, 20)
        延迟 (随机延迟)
        .如果真 (n ＞ 最大循环次数)
            跳出循环 ()
        .如果真结束

    .判断循环尾 ()

    n ＝ 0
    最大循环次数 ＝ 取随机数 (35, 55)
    .判断循环首 (判断是否需要按空格 () ＝ 1)
        n ＝ n ＋ 1
        随机延迟 ＝ 取随机数 (120, 220)
        延迟 (随机延迟)

        .如果真 (n ＞ 最大循环次数)
            跳出循环 ()
        .如果真结束

        .如果 (鱼的数量 ≥ 95)
            dd键鼠.KeyPress (#退格键, 1)
        .否则
            dd键鼠.KeyPress (#空格键, 1)

        .如果结束

    .判断循环尾 ()

    窗口_将焦点切换到指定的窗口 (窗口句柄, 真)
    随机延迟 ＝ 取随机数 (180, 280)
    延时 (随机延迟)

    dd键鼠.KeyDown (#Shift键, 1)
    dd键鼠.LeftDown (1)

    随机延迟 ＝ 取随机数 (180, 280)
    延时 (随机延迟)
    dd键鼠.LeftUp (1)

    随机延迟 ＝ 取随机数 (40, 80)
    延迟 (随机延迟)
    dd键鼠.KeyUp (#Shift键, 1)

    随机延迟 ＝ 取随机数 (1400, 1700)
    延时 (随机延迟)

.如果真结束



' dd键鼠.KeyPress (#键0, 1)
手里的杆子 ＝ 杆子索引
正在处理 ＝ 假

.子程序 _选择框1_被单击

.如果真 (选择框1.选中 ＝ 真)
    时钟1.时钟周期 ＝ 100
.如果真结束
.如果真 (选择框1.选中 ＝ 假)
    时钟1.时钟周期 ＝ 0
.如果真结束


.子程序 _时钟2_周期事件

杆子1状态 ＝ mem.内存_读整数型 (PID, 杆子1地址, 0)
杆子2状态 ＝ mem.内存_读整数型 (PID, 杆子2地址, 0)
杆子3状态 ＝ mem.内存_读整数型 (PID, 杆子3地址, 0)
' 调试输出 (杆子1状态, 杆子2状态, 杆子3状态)
.如果真 (正在处理 ＝ 假)
    .如果 (杆子1状态 ＝ 1)
        处理收杆_飞德 (1)
    .否则
        .如果 (杆子2状态 ＝ 1)
            处理收杆_飞德 (2)
        .否则
            .如果 (杆子3状态 ＝ 1)
                处理收杆_飞德 (3)
            .否则

            .如果结束

        .如果结束

    .如果结束

.如果真结束


.子程序 _选择框2_被单击

.如果真 (选择框2.选中 ＝ 真)
    时钟2.时钟周期 ＝ 100
.如果真结束
.如果真 (选择框2.选中 ＝ 假)
    时钟2.时钟周期 ＝ 0
.如果真结束



.子程序 _选择框3_被单击

.如果 (选择框3.选中)
    时钟3.时钟周期 ＝ 10000

.否则
    时钟3.时钟周期 ＝ 0

.如果结束



.子程序 _时钟3_周期事件
.局部变量 flag, 逻辑型


.如果真 (正在处理 ＝ 假)

    .如果真 (判断点的位置颜色是否正确 (300, 121, “a2b032”, 1))

        正在处理 ＝ 真
        窗口_将焦点切换到指定的窗口 (窗口句柄, 真)
        dd键鼠.KeyPress (#键4, 1)
        延迟 (3500)
        dd键鼠.LeftClick (1)
        延迟 (2000)
        处理收鱼 ()

    .如果真结束
    正在处理 ＝ 假
.如果真结束



.子程序 _选择框13_被单击

.如果真 (选择框13.选中 ＝ 真)
    时钟4.时钟周期 ＝ 10000
.如果真结束
.如果真 (选择框13.选中 ＝ 假)
    时钟4.时钟周期 ＝ 0
.如果真结束


.子程序 _时钟4_周期事件
.局部变量 flag, 逻辑型


.如果真 (正在处理 ＝ 假)

    .如果真 (判断点的位置颜色是否正确 (270, 92, “b7c738-202020”, 1) ＝ 假)
        正在处理 ＝ 真
        窗口_将焦点切换到指定的窗口 (窗口句柄, 真)
        dd键鼠.KeyPress (#键5, 1)



    .如果真结束
    正在处理 ＝ 假
.如果真结束



.子程序 初始化列表框

列表框1.加入项目 (“图1鱼市,243.0,3.0,423.0”, )
列表框1.加入项目 (“图1咖啡馆,226.296,5.633,427.443”, )
列表框1.加入项目 (“图1钓鱼站,240.533,2.87684,461.641”, )
列表框1.加入项目 (“图1食品店,232.724,4.94948,429.302”, )
列表框1.加入项目 (“图1钓欧鲌,414.599,0.434263,432.073”, )
列表框1.加入项目 (“图2钓鱼站,162.907,2.25586,351.686”, )
列表框1.加入项目 (“图2鱼市,138.476,2.22013,334.703”, )
列表框1.加入项目 (“图3鱼市,-116.837,1.77343,-50.2998”, )
列表框1.加入项目 (“图3钓鱼站,-103.153,3.99339,-74.6437”, )
列表框1.加入项目 (“老奥钓欧鳊,257.88,-0.0252821,159.691”, )
列表框1.加入项目 (“老奥钓鱼站,234.701,4.29915,262.552”, )
列表框1.加入项目 (“老奥鱼市,221.725,3.68692,218.99”, )
列表框1.加入项目 (“老奥钓欧鲌,220.163,0.521597,283.266”, )
列表框1.加入项目 (“老奥钓草鱼,185.28,0.181976,310.229”, )
列表框1.加入项目 (“老奥钓点,103.854,0.358401,335.786”, )


.子程序 _按钮5_被单击
.局部变量 人物坐标, D3D坐标
.局部变量 内容, 文本型

人物坐标 ＝ 读当前坐标 ()
输入框 (“请输入坐标名称”, , “”, 内容, 1, )

调试输出 (内容 ＋ “,” ＋ 到文本 (人物坐标.x) ＋ “,” ＋ 到文本 (人物坐标.z) ＋ “,” ＋ 到文本 (人物坐标.y))


.子程序 _列表框1_列表项被选择
.局部变量 文本, 文本型
.局部变量 文本数组, 文本型, , "0"

文本 ＝ 列表框1.取项目文本 (列表框1.现行选中项)
文本数组 ＝ 分割文本 (文本, “,”, )

传送 (到小数 (文本数组 [2]), 到小数 (文本数组 [3]), 到小数 (文本数组 [4]))


.子程序 __启动窗口_将被销毁

mem.卸载驱动 ()
mem.进程_保护进程 (PID, 3, 假)
' mem.进程_隐藏进程 (-1, 假)


.子程序 _按钮1_被单击


