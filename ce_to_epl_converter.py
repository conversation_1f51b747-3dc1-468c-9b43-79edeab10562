#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CE (Cheat Engine) XML to 易语言 (Easy Language) Converter
将CE的CheatTable XML格式转换为易语言代码
"""

import xml.etree.ElementTree as ET
import re
from typing import List, Dict, Any


class CEToEPLConverter:
    def __init__(self):
        self.variable_types = {
            "4 Bytes": "整数型",
            "8 Bytes": "长整数型", 
            "Float": "小数型",
            "Double": "双精度小数型",
            "2 Bytes": "短整数型",
            "Byte": "字节型",
            "String": "文本型"
        }
    
    def parse_xml(self, xml_content: str) -> List[Dict[str, Any]]:
        """解析CE的XML内容"""
        try:
            root = ET.fromstring(xml_content)
            entries = []
            
            for entry in root.findall('.//CheatEntry'):
                entry_data = {}
                
                # 获取ID
                id_elem = entry.find('ID')
                entry_data['id'] = id_elem.text if id_elem is not None else "0"
                
                # 获取描述
                desc_elem = entry.find('Description')
                entry_data['description'] = desc_elem.text if desc_elem is not None else ""
                
                # 获取变量类型
                var_type_elem = entry.find('VariableType')
                entry_data['variable_type'] = var_type_elem.text if var_type_elem is not None else "4 Bytes"
                
                # 获取基址
                address_elem = entry.find('Address')
                if address_elem is not None:
                    address = address_elem.text
                    # 解析模块名和偏移
                    if '+' in address:
                        parts = address.split('+')
                        module_name = parts[0].strip('"')
                        base_offset = parts[1] if len(parts) > 1 else "0"
                    else:
                        module_name = ""
                        base_offset = address
                    
                    entry_data['module'] = module_name
                    entry_data['base_offset'] = base_offset
                
                # 获取偏移链
                offsets_elem = entry.find('Offsets')
                offsets = []
                if offsets_elem is not None:
                    for offset in offsets_elem.findall('Offset'):
                        offsets.append(offset.text)
                
                entry_data['offsets'] = offsets
                entries.append(entry_data)
            
            return entries
            
        except ET.ParseError as e:
            print(f"XML解析错误: {e}")
            return []
    
    def convert_to_hex_function(self, hex_value: str) -> str:
        """将十六进制值转换为易语言的进制转换函数调用"""
        # 移除可能的0x前缀
        hex_value = hex_value.replace('0x', '').replace('0X', '')
        return f'进制_十六到十 ("{hex_value.upper()}")'
    
    def generate_epl_code(self, entries: List[Dict[str, Any]]) -> str:
        """生成易语言代码"""
        if not entries:
            return ""
        
        code_lines = []
        code_lines.append(".版本 2")
        code_lines.append("")
        
        for i, entry in enumerate(entries):
            # 添加注释
            if entry.get('description'):
                code_lines.append(f"// {entry['description']}")
            
            # 生成函数或代码块
            function_name = f"读取内存值_{entry['id']}" if entry['id'] != "0" else "读取内存值"
            
            code_lines.append(f".子程序 {function_name}, {self.get_epl_type(entry['variable_type'])}")
            code_lines.append("")
            
            # 局部变量声明
            code_lines.append(".局部变量 模块, 长整数型")
            code_lines.append(".局部变量 temp, 长整数型")
            result_type = self.get_epl_type(entry['variable_type'])
            code_lines.append(f".局部变量 结果, {result_type}")
            code_lines.append("")
            
            # 获取模块地址
            if entry.get('module'):
                code_lines.append(f'模块 ＝ mem.取模块地址 (PID, "{entry["module"]}")')
            else:
                code_lines.append("模块 ＝ 0  // 没有指定模块")
            
            # 基址计算
            if entry.get('base_offset') and entry['base_offset'] != "0":
                base_offset_call = self.convert_to_hex_function(entry['base_offset'])
                code_lines.append(f"temp ＝ mem.读长整数 (PID, 模块 ＋ {base_offset_call})")
            else:
                code_lines.append("temp ＝ 模块")
            
            # 偏移链处理
            offsets = entry.get('offsets', [])
            if offsets:
                # 反转偏移链顺序（CE中的偏移链是反向的）
                for offset in offsets[:-1]:  # 除了最后一个偏移
                    offset_call = self.convert_to_hex_function(offset)
                    code_lines.append(f"temp ＝ mem.读长整数 (PID, temp ＋ {offset_call})")
                
                # 最后一个偏移，读取最终值
                final_offset = offsets[-1]
                final_offset_call = self.convert_to_hex_function(final_offset)
                read_function = self.get_read_function(entry['variable_type'])
                code_lines.append(f"结果 ＝ mem.{read_function} (PID, temp ＋ {final_offset_call})")
            else:
                # 没有偏移链，直接读取
                read_function = self.get_read_function(entry['variable_type'])
                code_lines.append(f"结果 ＝ mem.{read_function} (PID, temp)")
            
            code_lines.append("返回 (结果)")
            code_lines.append("")
        
        return "\n".join(code_lines)
    
    def get_epl_type(self, ce_type: str) -> str:
        """获取易语言对应的数据类型"""
        return self.variable_types.get(ce_type, "整数型")
    
    def get_read_function(self, ce_type: str) -> str:
        """根据CE类型获取对应的易语言读取函数"""
        type_to_function = {
            "4 Bytes": "读整数型",
            "8 Bytes": "读长整数",
            "Float": "读小数型", 
            "Double": "读双精度小数型",
            "2 Bytes": "读短整数型",
            "Byte": "读字节型",
            "String": "读文本型"
        }
        return type_to_function.get(ce_type, "读整数型")


def main():
    """主函数 - 示例用法"""
    # 示例XML内容
    sample_xml = '''<?xml version="1.0" encoding="utf-8"?>
<CheatTable>
  <CheatEntries>
    <CheatEntry>
      <ID>0</ID>
      <Description>"pointerscan result"</Description>
      <LastState Value="12" RealAddress="75FD6B18"/>
      <VariableType>4 Bytes</VariableType>
      <Address>"GameAssembly.dll"+03D44020</Address>
      <Offsets>
        <Offset>218</Offset>
        <Offset>B8</Offset>
        <Offset>20</Offset>
        <Offset>1D0</Offset>
        <Offset>2E8</Offset>
        <Offset>B8</Offset>
        <Offset>20</Offset>
      </Offsets>
    </CheatEntry>
  </CheatEntries>
</CheatTable>'''
    
    converter = CEToEPLConverter()
    entries = converter.parse_xml(sample_xml)
    epl_code = converter.generate_epl_code(entries)
    
    print("转换后的易语言代码:")
    print("=" * 50)
    print(epl_code)


if __name__ == "__main__":
    main()
