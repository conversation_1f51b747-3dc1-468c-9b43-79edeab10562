@echo off
echo CE转易语言转换器 - 打包为EXE
echo ================================

echo 正在检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo.
echo 正在安装PyInstaller...
pip install pyinstaller
if %errorlevel% neq 0 (
    echo 错误: PyInstaller安装失败
    pause
    exit /b 1
)

echo.
echo 正在打包程序...
pyinstaller --onefile --windowed --name "CE转易语言转换器" --icon=icon.ico ce_to_epl_gui.py

if %errorlevel% eq 0 (
    echo.
    echo 打包成功！
    echo EXE文件位置: dist\CE转易语言转换器.exe
    echo.
    echo 正在打开dist文件夹...
    explorer dist
) else (
    echo.
    echo 打包失败，请检查错误信息
)

echo.
pause
