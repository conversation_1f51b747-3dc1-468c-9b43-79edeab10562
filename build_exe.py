#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动打包脚本 - 将CE转换器打包为exe
"""

import subprocess
import sys
import os

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("PyInstaller安装成功！")
        return True
    except subprocess.CalledProcessError:
        print("PyInstaller安装失败！")
        return False

def build_exe():
    """打包exe"""
    print("正在打包程序...")
    
    # PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",           # 单文件
        "--windowed",          # 无控制台窗口
        "--name", "CE转易语言转换器",  # exe名称
        "ce_to_epl_gui.py"     # 源文件
    ]
    
    try:
        subprocess.check_call(cmd)
        print("\n打包成功！")
        print("EXE文件位置: dist\\CE转易语言转换器.exe")
        
        # 检查文件是否存在
        exe_path = "dist\\CE转易语言转换器.exe"
        if os.path.exists(exe_path):
            size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"文件大小: {size:.1f} MB")
            
            # 尝试打开dist文件夹
            try:
                os.startfile("dist")
            except:
                print("请手动打开dist文件夹查看生成的exe文件")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        return False
    except FileNotFoundError:
        print("错误: 未找到pyinstaller命令")
        print("请确保PyInstaller已正确安装")
        return False

def main():
    print("CE转易语言转换器 - 自动打包工具")
    print("=" * 40)
    
    # 检查源文件是否存在
    if not os.path.exists("ce_to_epl_gui.py"):
        print("错误: 未找到源文件 ce_to_epl_gui.py")
        input("按回车键退出...")
        return
    
    # 安装PyInstaller
    if not install_pyinstaller():
        input("按回车键退出...")
        return
    
    print()
    
    # 打包exe
    if build_exe():
        print("\n打包完成！你现在可以分发这个exe文件了。")
    else:
        print("\n打包失败，请检查错误信息。")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
