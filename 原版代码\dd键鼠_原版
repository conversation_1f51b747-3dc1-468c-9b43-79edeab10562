.版本 2

.子程序 FiveClick, , , 驱动级模拟鼠标5键单击
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 FiveDown, , , 驱动级模拟鼠标5键按下
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 FiveUp, , , 驱动级模拟鼠标5键弹起
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 FourClick, , , 驱动级模拟鼠标4键单击
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 FourDown, , , 驱动级模拟鼠标4键按下
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 FourUp, , , 驱动级模拟鼠标4键弹起
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 KeyDown, , , 驱动级模拟键盘按下
.参数 按键码, 整数型, , 
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 KeyGroup, , , 驱动级模拟键盘组合键
.参数 组合键, 文本型, , 例子：Ctrl+Alt+Delete

.版本 2

.子程序 KeyPress, , , 驱动级模拟键盘输入
.参数 按键码, 整数型, , 
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 KeyUp, , , 驱动级模拟键盘弹起
.参数 按键码, 整数型, , 
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 LeftClick, , , 驱动级模拟鼠标左键单击
.参数 次数, 整数型, 可空 , 
.版本 2

.子程序 LeftDoubleClick, , , 驱动级模拟鼠标左键双击
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 LeftDown, , , 驱动级模拟鼠标左键按下
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 LeftUp, , , 驱动级模拟鼠标左键弹起
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 MiddleClick, , , 驱动级模拟鼠标中键单击
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 MiddleDown, , , 驱动级模拟鼠标中键按下
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 MiddleUp, , , 驱动级模拟鼠标中键弹起
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 MouseWheel, , , 驱动级模拟鼠标滚轮
.参数 滚动量, 整数型, , 

.版本 2

.子程序 MoveR, , , 驱动级模拟鼠标相对移动
.参数 X偏移, 整数型, , 
.参数 Y偏移, 整数型, , 

.版本 2

.子程序 MoveTo, , , 驱动级模拟鼠标绝对移动
.参数 X坐标, 整数型, , 
.参数 Y坐标, 整数型, , 

.版本 2

.子程序 RightClick, , , 驱动级模拟鼠标右键单击
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 RightDoubleClick, , , 驱动级模拟鼠标右键双击
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 RightDown, , , 驱动级模拟鼠标右键按下
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 RightUp, , , 驱动级模拟鼠标右键弹起
.参数 次数, 整数型, 可空 , 

.版本 2

.子程序 SayString, , , 驱动级模拟文字输入，自动区分大小写，允许输入中文
.参数 字符串, 文本型, , 
.参数 编码, 文本型, 可空 , 默认W=Unicode编码，A=Ansi编码
