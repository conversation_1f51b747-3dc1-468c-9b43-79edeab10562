.版本 2

.子程序 安装驱动, 逻辑型, , 


.版本 2

.子程序 读小数型, 小数型, , 
.参数 进程ID, 整数型, , 
.参数 内存地址, 长整数型, , 


.版本 2

.子程序 读长整数, 长整数型, , 
.参数 进程ID, 整数型, , 
.参数 内存地址, 长整数型, , 

.版本 2

.子程序 读整数型, 整数型, , 
.参数 进程ID, 整数型, , 
.参数 内存地址, 长整数型, , 


.版本 2

.子程序 读字节集, 字节集, , 
.参数 进程ID, 整数型, , 
.参数 内存地址, 长整数型, , 
.参数 读取长度, 长整数型, , 

.版本 2

.子程序 读字节型, 字节型, , 
.参数 进程ID, 整数型, , 
.参数 内存地址, 长整数型, , 

.版本 2

.子程序 取模块地址, 长整数型, , 
.参数 进程ID, 整数型, , 
.参数 模块名, 文本型, , 

.版本 2

.子程序 设置保护进程ID, 逻辑型, , 
.参数 进程ID, 整数型, , 

.版本 2

.子程序 写小数型, 逻辑型, , 
.参数 进程ID, 整数型, , 
.参数 内存地址, 长整数型, , 
.参数 写入数据, 小数型, , 

.版本 2

.子程序 写长整数, 逻辑型, , 
.参数 进程ID, 整数型, , 
.参数 内存地址, 长整数型, , 
.参数 写入数据, 长整数型, , 

.版本 2

.子程序 写整数型, 逻辑型, , 
.参数 进程ID, 整数型, , 
.参数 内存地址, 长整数型, , 
.参数 写入数据, 整数型, , 

.版本 2

.子程序 写字节集, 逻辑型, , 
.参数 进程ID, 整数型, , 
.参数 内存地址, 长整数型, , 
.参数 写入内容, 字节集, , 

.版本 2

.子程序 写字节型, 逻辑型, , 
.参数 进程ID, 整数型, , 
.参数 内存地址, 长整数型, , 
.参数 写入数据, 字节型, , 
.版本 2

.子程序 卸载驱动, 逻辑型, , 
